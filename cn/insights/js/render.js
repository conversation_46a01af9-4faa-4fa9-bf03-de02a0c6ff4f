// Google Tag Manager
;(function (w, d, s, l, i) {
  w[l] = w[l] || []
  w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' })
  var f = d.getElementsByTagName(s)[0],
    j = d.createElement(s),
    dl = l != 'dataLayer' ? '&l=' + l : ''
  j.async = true
  j.src = '../../../www.googletagmanager.com/gtm5445.html?id=' + i + dl
  f.parentNode.insertBefore(j, f)
})(window, document, 'script', 'dataLayer', 'GTM-W4DR9R')

// OneTrust Cookies Consent Notice
function OptanonWrapper() {}

// 动态渲染文章列表
function renderArticleItems(articles, pagination = null) {
  const container = document.querySelector('.facetwp-template .row')
  if (!container) {
    console.error('找不到文章容器')
    return
  }

  // 清空现有内容（保留 fwp-loop 注释）
  container.innerHTML = '<!--fwp-loop-->'

  // 遍历文章数据并生成HTML
  articles.forEach((article, index) => {
    const articleElement = createArticleElement(article)
    container.appendChild(articleElement)

    // 添加渐入动画效果
    setTimeout(() => {
      const articleItem = articleElement.querySelector('.crw-article-item')
      if (articleItem) {
        articleItem.classList.add('loaded')
      }
    }, index * 100) // 每个元素延迟100ms显示
  })

  // 更新分页控件
  if (pagination) {
    updatePagination(pagination)
  }
}

// 创建单个文章元素
function createArticleElement(article) {
  const colDiv = document.createElement('div')
  colDiv.className = 'col-lg-3'

  colDiv.innerHTML = `
            <div class="crw-article-item">
                <a href="${article.url || '#'}">
                    <span class="article-top">
                        <span class="article-img" style="background-image: url('https://crownwwcn.com/prod-api${
                          article.cover ||
                          '../wp-content/uploads/sites/7/2025/03/default-article.jpg'
                        }');"></span>
                        <span class="article-cat">${
                          article.category || '未分类'
                        }</span>
                        <span class="article-title">${
                          article.title || '无标题'
                        }</span>
                    </span>
                    <span class="article-bottom">
                        <span class="btn-text">阅读更多 <i class="icon icon-arrow-button"></i></span>
                    </span>
                </a>
            </div>
        `

  return colDiv
}

// 显示错误状态
function showError(message) {
  const container = document.querySelector('.facetwp-template .row')
  if (container) {
    container.innerHTML = `
                <!--fwp-loop-->
                <div class="col-lg-12 text-center">
                    <div class="error-message">
                        <p>加载失败: ${message}</p>
                    </div>
                </div>
            `
  }
}

// 配置FacetWP和懒加载
function setupFacetWP(topics) {
  // 动态加载 FacetWP 前端脚本
  const script = document.createElement('script')
  script.src =
    '../wp-content/plugins/facetwp/assets/js/dist/front.min7035.js?ver=4.3.4'
  script.async = true
  script.onload = function () {
    console.log('FacetWP 脚本加载成功')
  }
  script.onerror = function () {
    console.error('FacetWP 脚本加载失败')
  }
  document.head.appendChild(script)

  window.FWP_JSON = {
    prefix: '_',
    no_results_text: 'No results found',
    ajaxurl: 'https://www.crownrms.com/cn/wp-json/facetwp/v1/refresh',
    nonce: '6af19a93be',
    preload_data: {
      facets: {
        search:
          '<span class="facetwp-input-wrap"><i class="facetwp-icon" data-ts="\u641c\u7d22"></i><input type="text" class="facetwp-search" value="" placeholder="\u8f93\u5165\u5173\u952e\u5b57" autocomplete="off" /></span>',
        topics: `
        <select class="facetwp-dropdown"><option value="">\u6240\u6709\u4e3b\u9898<\/option>
        ${topics
          .map(
            (topic) =>
              `<option value="${topic.typeValue}" data-counter="${topic.newsCount}" class="d0">${topic.typeLabel}</option>`,
          )
          .join('')}
        </select>
        `,
        industries:
          '<select class="facetwp-dropdown"><option value="">\u6240\u6709\u884c\u4e1a</option></select>',
        pagination:
          '<div class="facetwp-pager"><a class="facetwp-page first active" data-page="1">1</a><a class="facetwp-page" data-page="2">2</a><a class="facetwp-page last" data-page="3">3</a><a class="facetwp-page next" data-page="2">\u4e0b\u4e00\u9875 \u00bb</a></div>',
      },
      template: '',
      settings: {
        debug: 'Enable debug mode in [Settings > FacetWP > Settings]',
        pager: {
          page: 1,
          per_page: 12,
          total_rows: 35,
          total_rows_unfiltered: 35,
          total_pages: 3,
        },
        num_choices: { topics: 10, industries: 0 },
        labels: {
          search: 'Search',
          topics: 'Topics',
          industries: 'Industries',
          pagination: 'Pagination',
        },
        search: { auto_refresh: 'no' },
        topics: {
          placeholder: '\u6240\u6709\u4e3b\u9898',
          overflowText: '{n} selected',
          searchText: 'Search',
          noResultsText: 'No results found',
          operator: 'and',
        },
        industries: {
          placeholder: '\u6240\u6709\u884c\u4e1a',
          overflowText: '{n} selected',
          searchText: 'Search',
          noResultsText: 'No results found',
          operator: 'and',
        },
        pagination: {
          pager_type: 'numbers',
          scroll_target: '',
          scroll_offset: 0,
        },
      },
    },
  }
  window.FWP_HTTP = { get: [], uri: 'cn/insights', url_vars: [] }

  setupLazyLoad()
}

// 设置懒加载
function setupLazyLoad() {
  window.lazyLoadOptions = [
    {
      elements_selector:
        'img[data-lazy-src],.rocket-lazyload,iframe[data-lazy-src]',
      data_src: 'lazy-src',
      data_srcset: 'lazy-srcset',
      data_sizes: 'lazy-sizes',
      class_loading: 'lazyloading',
      class_loaded: 'lazyloaded',
      threshold: 300,
      callback_loaded: function (element) {
        if (
          element.tagName === 'IFRAME' &&
          element.dataset.rocketLazyload == 'fitvidscompatible'
        ) {
          if (element.classList.contains('lazyloaded')) {
            if (typeof window.jQuery != 'undefined') {
              if (jQuery.fn.fitVids) {
                jQuery(element).parent().fitVids()
              }
            }
          }
        }
      },
    },
    {
      elements_selector: '.rocket-lazyload',
      data_src: 'lazy-src',
      data_srcset: 'lazy-srcset',
      data_sizes: 'lazy-sizes',
      class_loading: 'lazyloading',
      class_loaded: 'lazyloaded',
      threshold: 300,
    },
  ]

  window.addEventListener(
    'LazyLoad::Initialized',
    function (e) {
      var lazyLoadInstance = e.detail.instance
      if (window.MutationObserver) {
        var observer = new MutationObserver(function (mutations) {
          var image_count = 0
          var iframe_count = 0
          var rocketlazy_count = 0
          mutations.forEach(function (mutation) {
            for (var i = 0; i < mutation.addedNodes.length; i++) {
              if (
                typeof mutation.addedNodes[i].getElementsByTagName !==
                'function'
              ) {
                continue
              }
              if (
                typeof mutation.addedNodes[i].getElementsByClassName !==
                'function'
              ) {
                continue
              }
              images = mutation.addedNodes[i].getElementsByTagName('img')
              is_image = mutation.addedNodes[i].tagName == 'IMG'
              iframes = mutation.addedNodes[i].getElementsByTagName('iframe')
              is_iframe = mutation.addedNodes[i].tagName == 'IFRAME'
              rocket_lazy =
                mutation.addedNodes[i].getElementsByClassName('rocket-lazyload')
              image_count += images.length
              iframe_count += iframes.length
              rocketlazy_count += rocket_lazy.length
              if (is_image) {
                image_count += 1
              }
              if (is_iframe) {
                iframe_count += 1
              }
            }
          })
          if (image_count > 0 || iframe_count > 0 || rocketlazy_count > 0) {
            lazyLoadInstance.update()
          }
        })
        var b = document.getElementsByTagName('body')[0]
        var config = { childList: !0, subtree: !0 }
        observer.observe(b, config)
      }
    },
    !1,
  )
}

// 更新分页控件
function updatePagination(pagination) {
  const paginationContainer = document.querySelector(
    '.facetwp-facet-pagination',
  )
  if (!paginationContainer) return

  const { page, totalPages } = pagination

  let paginationHTML = ''

  // 添加"上一页"按钮
  if (page > 1) {
    paginationHTML += `<a class="facetwp-page prev" data-page="${
      page - 1
    }" onclick="window.loadArticles(${page - 1})">« 上一页</a>`
  }

  // 添加页码按钮
  for (let i = 1; i <= totalPages; i++) {
    const activeClass = i === page ? 'active' : ''
    paginationHTML += `<a class="facetwp-page ${activeClass}" data-page="${i}" onclick="window.loadArticles(${i})">${i}</a>`
  }

  // 添加"下一页"按钮
  if (page < totalPages) {
    paginationHTML += `<a class="facetwp-page next" data-page="${
      page + 1
    }" onclick="window.loadArticles(${page + 1})">下一页 »</a>`
  }

  paginationContainer.innerHTML = paginationHTML
}

// 导出函数
export {
  renderArticleItems,
  createArticleElement,
  showError,
  setupFacetWP,
  updatePagination,
}
