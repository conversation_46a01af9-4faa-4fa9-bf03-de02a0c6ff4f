import { renderArticleItems, showError, setupFacetWP } from './render.js'

// 主要的数据加载函数
function loadArticles(page = 1, pageSize = 12) {
  return fetch(
    `/prod-api/api/web/doc/news/list?pageNum=${page}&pageSize=${pageSize}`,
  )
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`)
      }
      return res.json()
    })
    .then((data) => {
      console.log('API 返回数据:', data)

      // 根据实际API返回结构调整数据路径
      const articles = data.data?.rows || []
      const total = data.data?.total || 0

      if (articles.length > 0) {
        renderArticleItems(articles, {
          page: page,
          pageSize: pageSize,
          total: total,
          totalPages: Math.ceil(total / pageSize),
        })
      } else {
        showError('没有找到文章数据')
      }

      return {
        articles,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
      }
    })
    .catch((error) => {
      console.error('加载文章失败:', error)
      showError(error.message)
      throw error
    })
}

// 获取新闻主题字典数据
function getDocNewTopic() {
  return fetch(`/prod-api/api/web/doc/news/type/list`)
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`)
      }
      return res.json()
    })
    .then((data) => {
      const topics = data.data || []
      setupFacetWP(topics)
      return topics
    })
    .catch((error) => {
      console.error('获取新闻主题失败:', error)
      throw error
    })
}

// 获取字典数据的通用函数
function getDic(dictType = 'service') {
  return fetch(`/prod-api/api/web/system/dict/data/type/${dictType}`)
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`)
      }
      return res.json()
    })
    .then((data) => {
      return data.data || []
    })
    .catch((error) => {
      console.error(`获取字典数据失败 (${dictType}):`, error)
      throw error
    })
}

// 初始化页面数据
async function initializePage(page = 1, pageSize = 12) {
  try {
    // 并行加载文章和主题数据
    const [articlesPromise, topicsPromise] = await Promise.allSettled([
      loadArticles(page, pageSize),
      getDocNewTopic(),
    ])

    // 处理加载结果
    if (articlesPromise.status === 'rejected') {
      console.error('文章加载失败:', articlesPromise.reason)
    }

    if (topicsPromise.status === 'rejected') {
      console.error('主题加载失败:', topicsPromise.reason)
    }

    console.log('页面初始化完成')
  } catch (error) {
    console.error('页面初始化失败:', error)
  }
}

// 导出函数
export { loadArticles, getDocNewTopic, getDic, initializePage }
